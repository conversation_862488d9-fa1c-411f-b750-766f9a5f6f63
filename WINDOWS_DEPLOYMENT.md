# Windows Deployment Guide

## 🚨 ACME.json Permissions Issue on Windows

The error `permissions 777 for acme.json are too open, please use 600` occurs because:
1. Windows file permissions work differently than Linux
2. Docker on Windows may not properly handle chmod commands
3. Traefik requires strict permissions for security

## ✅ Solutions

### **Option 1: Use WSL2 (Recommended for Production)**

If deploying to production, use WSL2 or a Linux server:

```bash
# In WSL2 or Linux server
chmod 600 acme.json
docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d
```

### **Option 2: Create acme.json inside Docker (Windows)**

For Windows development/testing:

```bash
# Create acme.json with proper permissions inside container
docker run --rm -v ${PWD}:/workspace alpine sh -c "touch /workspace/acme.json && chmod 600 /workspace/acme.json"
```

### **Option 3: Modified Production Setup for Windows**

I've created a Windows-compatible production setup:

```bash
# Use the production configuration
docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d
```

## 🔧 Current Development Setup (Fixed)

For development on Windows:
- ✅ **No acme.json mounting** in development docker-compose.yml
- ✅ **Certificate resolver disabled** in traefik.yml
- ✅ **No ACME errors** in development
- ✅ **Website works** at http://localhost

## 🚀 Production Deployment

### **On Linux Server:**
```bash
# 1. Upload files to server
scp -r * user@server:/path/to/deployment/

# 2. Set permissions
chmod 600 acme.json

# 3. Deploy
docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d
```

### **On Windows (for testing):**
```bash
# 1. Create acme.json with Docker
docker run --rm -v ${PWD}:/workspace alpine sh -c "touch /workspace/acme.json && chmod 600 /workspace/acme.json"

# 2. Deploy
docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d
```

## 📋 File Permissions Reference

| File | Required Permissions | Purpose |
|------|---------------------|---------|
| `acme.json` | `600` (rw-------) | Let's Encrypt certificates |
| `traefik.yml` | `644` (rw-r--r--) | Traefik configuration |
| `docker-compose.yml` | `644` (rw-r--r--) | Docker configuration |

## 🛠 Troubleshooting

### If you still get ACME errors:
1. **Check file exists**: `ls -la acme.json`
2. **Check permissions**: Should be `-rw-------` (600)
3. **Recreate file**: `rm acme.json && touch acme.json && chmod 600 acme.json`
4. **Use Docker method**: `docker run --rm -v ${PWD}:/workspace alpine sh -c "touch /workspace/acme.json && chmod 600 /workspace/acme.json"`

### For production deployment:
- Always use a Linux server for production
- Windows is fine for development but not recommended for production
- The production files are ready and tested
