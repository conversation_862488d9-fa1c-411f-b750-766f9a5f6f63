version: '3.8'

services:
  # Traefik reverse proxy
  traefik:
    image: traefik:v3.0
    container_name: traefik
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik.prod.yml:/traefik.yml:ro
      - ./acme.json:/acme.json
    networks:
      - traefik
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.dashboard.rule=Host(`traefik.${DOMAIN:-brightone.com.au}`)"
      - "traefik.http.routers.dashboard.entrypoints=web,websecure"
      - "traefik.http.routers.dashboard.tls=true"
      - "traefik.http.routers.dashboard.tls.certresolver=letsencrypt"

  # Hello World website
  helloworld:
    build: .
    container_name: helloworld-web
    restart: unless-stopped
    volumes:
      - ./index.html:/usr/share/nginx/html/index.html:ro
    networks:
      - traefik
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.helloworld.rule=Host(`${DOMAIN:-brightone.com.au}`)"
      - "traefik.http.routers.helloworld.entrypoints=web,websecure"
      - "traefik.http.routers.helloworld.tls=true"
      - "traefik.http.routers.helloworld.tls.certresolver=letsencrypt"
      - "traefik.http.services.helloworld.loadbalancer.server.port=80"
      # Redirect HTTP to HTTPS
      - "traefik.http.middlewares.redirect-to-https.redirectscheme.scheme=https"
      - "traefik.http.routers.helloworld-http.rule=Host(`${DOMAIN:-brightone.com.au}`)"
      - "traefik.http.routers.helloworld-http.entrypoints=web"
      - "traefik.http.routers.helloworld-http.middlewares=redirect-to-https"

networks:
  traefik:
    external: false
