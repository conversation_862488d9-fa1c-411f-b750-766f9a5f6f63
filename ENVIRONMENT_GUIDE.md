# Environment Configuration Guide

## 🔧 Current Setup

You have **TWO** separate configurations:

### 1. **Development Environment** (Current)
- **Files**: `docker-compose.yml` + `traefik.yml` + `.env`
- **Domain**: `localhost`
- **HTTPS**: Disabled
- **Certificate Resolver**: Commented out
- **Usage**: `docker-compose up -d`

### 2. **Production Environment** (Ready for deployment)
- **Files**: `docker-compose.prod.yml` + `traefik.prod.yml` + `.env.prod`
- **Domain**: `brightone.com.au`
- **HTTPS**: Enabled with Let's Encrypt
- **Certificate Resolver**: Active
- **Usage**: `docker-compose -f docker-compose.prod.yml up -d`

## 🚨 **IMPORTANT**: Never Mix Configurations!

### ❌ **What Causes Errors:**
```bash
# DON'T DO THIS - mixing dev and prod configs
cp .env.prod .env  # This breaks development!
docker-compose up -d  # Uses dev compose with prod env = ERRORS
```

### ✅ **Correct Usage:**

#### **For Development (localhost):**
```bash
# Make sure .env has DOMAIN=localhost
docker-compose up -d
# Access: http://localhost
```

#### **For Production (brightone.com.au):**
```bash
# Use production files directly
docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d
# Access: https://brightone.com.au
```

## 🔄 **Switching Between Environments:**

### **Development → Production:**
```bash
# Stop development
docker-compose down

# Start production
docker-compose -f docker-compose.prod.yml --env-file .env.prod up -d
```

### **Production → Development:**
```bash
# Stop production
docker-compose -f docker-compose.prod.yml down

# Start development
docker-compose up -d
```

## 📋 **Quick Reference:**

| Environment | Compose File | Env File | Domain | HTTPS |
|-------------|--------------|----------|---------|-------|
| Development | `docker-compose.yml` | `.env` | localhost | ❌ |
| Production | `docker-compose.prod.yml` | `.env.prod` | brightone.com.au | ✅ |

## 🛠 **Current Status:**
- ✅ Development environment is running
- ✅ No certificate resolver errors
- ✅ No ACME permissions errors
- ✅ Website accessible at http://localhost
- ✅ Production files ready for deployment

## 🚨 **Windows ACME.json Fix Applied:**
- Development: acme.json mounting removed (not needed)
- Production: Use `docker-compose.prod.yml` with proper Linux permissions
- See `WINDOWS_DEPLOYMENT.md` for detailed Windows-specific instructions
