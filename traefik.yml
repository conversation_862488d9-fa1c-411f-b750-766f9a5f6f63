global:
  checkNewVersion: false
  sendAnonymousUsage: false

entryPoints:
  web:
    address: ":80"
  websecure:
    address: ":443"

providers:
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false

# certificatesResolvers:
#   letsencrypt:
#     acme:
#       email: <EMAIL>
#       storage: acme.json
#       httpChallenge:
#         entryPoint: web

api:
  dashboard: true
  insecure: true

log:
  level: INFO

accessLog: {}
