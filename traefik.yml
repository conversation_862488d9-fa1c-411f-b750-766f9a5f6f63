# Traefik configuration file
global:
  checkNewVersion: false
  sendAnonymousUsage: false

# Entry points
entryPoints:
  web:
    address: ":80"
    http:
      redirections:
        entrypoint:
          to: websecure
          scheme: https
  websecure:
    address: ":443"

# Providers
providers:
  docker:
    endpoint: "unix:///var/run/docker.sock"
    exposedByDefault: false
    network: traefik

# Certificate resolvers for Let's Encrypt
certificatesResolvers:
  letsencrypt:
    acme:
      email: <EMAIL>  # Change this to your email
      storage: acme.json
      httpChallenge:
        entryPoint: web

# API and dashboard
api:
  dashboard: true
  insecure: false  # Secure mode for production

# Logging
log:
  level: INFO

accessLog: {}
