# Hello World Website with <PERSON><PERSON><PERSON><PERSON>

This project contains a simple HTML Hello World website served through Traefik reverse proxy with automatic HTTPS certificates.

## Files Structure

- `index.html` - The main HTML website
- `Dockerfile` - Container configuration for the website
- `docker-compose.yml` - Docker Compose configuration
- `traefik.yml` - Traefik configuration
- `.env` - Environment variables
- `acme.json` - Let's Encrypt certificates storage

## Quick Start

### For Production (brightone.com.au)

1. Make sure your domain DNS points to your server
2. Start the services:
   ```bash
   docker-compose up -d
   ```

3. Access your website:
   - Website: https://brightone.com.au
   - Traefik Dashboard: https://traefik.brightone.com.au

### For Local Development

1. Edit `.env` file and change domain to localhost:
   ```
   DOMAIN=localhost
   ```

2. Comment out the certificate resolver in `traefik.yml`
3. Update docker-compose.yml labels to remove TLS
4. Start the services:
   ```bash
   docker-compose up -d
   ```

## Commands

- Start services: `docker-compose up -d`
- Stop services: `docker-compose down`
- View logs: `docker-compose logs -f`
- Rebuild website: `docker-compose up -d --build helloworld`

## Development Workflow

The `index.html` file is mounted as a volume, so you can:
1. Edit the `index.html` file directly
2. Save your changes
3. Refresh your browser - changes appear immediately!
4. No need to rebuild the Docker image for HTML/CSS/JS changes

## Features

- ✅ Automatic HTTPS with Let's Encrypt
- ✅ HTTP to HTTPS redirect
- ✅ Traefik dashboard for monitoring
- ✅ Docker containerized
- ✅ Easy domain configuration
- ✅ Live reload for development (volume mapping)

## Ports

- 80: HTTP (redirects to HTTPS)
- 443: HTTPS
- 8080: Traefik Dashboard (only accessible via domain in production)

## Domain Configuration

- **Main Website**: https://brightone.com.au
- **Traefik Dashboard**: https://traefik.brightone.com.au
- **Email for Let's Encrypt**: <EMAIL>
