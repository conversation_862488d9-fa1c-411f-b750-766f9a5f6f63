# Hello World Website with <PERSON><PERSON><PERSON><PERSON>

This project contains a simple HTML Hello World website served through Traefik reverse proxy with automatic HTTPS certificates.

## Files Structure

- `index.html` - The main HTML website
- `Dockerfile` - Container configuration for the website
- `docker-compose.yml` - Docker Compose configuration
- `traefik.yml` - Traefik configuration
- `.env` - Environment variables
- `acme.json` - Let's Encrypt certificates storage

## Quick Start

### For Local Development

1. Start the services:
   ```bash
   docker-compose up -d
   ```

2. Access your website:
   - Website: http://localhost
   - Traefik Dashboard: http://localhost:8080

### For Production

1. Edit `.env` file and set your domain:
   ```
   DOMAIN=yourdomain.com
   ```

2. Edit `traefik.yml` and set your email for Let's Encrypt:
   ```yaml
   certificatesResolvers:
     letsencrypt:
       acme:
         email: <EMAIL>
   ```

3. Start the services:
   ```bash
   docker-compose up -d
   ```

## Commands

- Start services: `docker-compose up -d`
- Stop services: `docker-compose down`
- View logs: `docker-compose logs -f`
- Rebuild website: `docker-compose up -d --build helloworld`

## Features

- ✅ Automatic HTTPS with Let's Encrypt
- ✅ HTTP to HTTPS redirect
- ✅ Traefik dashboard for monitoring
- ✅ Docker containerized
- ✅ Easy domain configuration

## Ports

- 80: HTTP (redirects to HTTPS)
- 443: HTTPS
- 8080: Traefik Dashboard
