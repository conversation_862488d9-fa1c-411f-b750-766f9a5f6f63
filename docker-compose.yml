version: '3.8'

services:
  # Traefik reverse proxy
  traefik:
    image: traefik:v3.0
    container_name: traefik
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./traefik.yml:/traefik.yml:ro
    networks:
      - traefik
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.dashboard.rule=Host(`traefik.${DOMAIN:-localhost}`)"
      - "traefik.http.routers.dashboard.entrypoints=web"

  # Hello World website
  helloworld:
    build: .
    container_name: helloworld-web
    restart: unless-stopped
    volumes:
      - ./index.html:/usr/share/nginx/html/index.html:ro
    networks:
      - traefik
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.helloworld.rule=Host(`${DOMAIN:-localhost}`)"
      - "traefik.http.routers.helloworld.entrypoints=web"
      - "traefik.http.services.helloworld.loadbalancer.server.port=80"

networks:
  traefik:
    external: false
