# Deployment Guide for brightone.com.au

## Prerequisites

1. **Domain Setup**: Ensure `brightone.com.au` and `traefik.brightone.com.au` DNS records point to your server's IP address
2. **Server Requirements**: Linux server with Docker and Docker Compose installed
3. **Firewall**: Ports 80 and 443 must be open to the internet

## DNS Configuration Required

Add these DNS records to your domain:

```
A    brightone.com.au        -> YOUR_SERVER_IP
A    traefik.brightone.com.au -> YOUR_SERVER_IP
```

## Production Deployment Steps

1. **Clone/Upload your files** to the server:
   ```bash
   # Upload all files to your server
   scp -r * user@your-server:/path/to/deployment/
   ```

2. **Set proper permissions** for acme.json:
   ```bash
   chmod 600 acme.json
   ```

3. **Use production configuration**:
   ```bash
   # Copy production environment
   cp .env.prod .env

   # Start with production compose file
   docker-compose -f docker-compose.prod.yml up -d
   ```

4. **Monitor the logs** for certificate generation:
   ```bash
   docker-compose -f docker-compose.prod.yml logs -f traefik
   ```

## What Happens During Deployment

1. **Traefik starts** and detects your services
2. **Let's Encrypt certificates** are automatically requested for:
   - `brightone.com.au`
   - `traefik.brightone.com.au`
3. **HTTP traffic** is automatically redirected to HTTPS
4. **Certificates** are stored in `acme.json` and auto-renewed

## Access URLs After Deployment

- **Main Website**: https://brightone.com.au
- **Traefik Dashboard**: https://traefik.brightone.com.au

## Troubleshooting

### Certificate Issues
```bash
# Check Traefik logs
docker-compose logs traefik

# Verify DNS resolution
nslookup brightone.com.au
nslookup traefik.brightone.com.au
```

### Service Issues
```bash
# Check container status
docker-compose ps

# Restart services
docker-compose restart
```

## Security Notes

- Traefik dashboard is secured with HTTPS and domain-based access
- Let's Encrypt certificates are automatically renewed
- HTTP traffic is redirected to HTTPS
- The `acme.json` file contains sensitive certificate data (keep secure)

## Local Development

To switch back to local development:

1. Change `.env`:
   ```
   DOMAIN=localhost
   ```

2. Update `docker-compose.yml` to remove TLS labels
3. Comment out certificate resolver in `traefik.yml`
4. Restart services
